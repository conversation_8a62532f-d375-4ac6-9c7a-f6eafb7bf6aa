<?php
/**
 * Debug database state during operations
 */

require_once 'config/config.php';
require_once 'src/utils/Database.php';
require_once 'src/models/ClientProject.php';

$db = Database::getInstance();
$clientProject = new ClientProject();
$projectId = 56;

echo "🔍 Debugging Database State During Operations\n";
echo "=============================================\n\n";

// Function to check database state
function checkDatabaseState($db, $projectId, $step) {
    echo "📊 Database state at step: $step\n";
    
    // Check connection
    $isHealthy = $db->isConnectionHealthy();
    echo "  - Connection healthy: " . ($isHealthy ? 'Yes' : 'No') . "\n";
    
    // Check checklist count
    $checklists = $db->fetchAll('SELECT * FROM project_checklists WHERE project_id = ?', [$projectId]);
    echo "  - Checklist count: " . count($checklists) . "\n";
    
    if (!empty($checklists)) {
        $checklist = $checklists[0];
        echo "  - Checklist ID: " . $checklist['id'] . "\n";
        echo "  - Completion: " . $checklist['completion_percentage'] . "%\n";
        echo "  - Updated: " . $checklist['updated_at'] . "\n";
        
        $data = json_decode($checklist['checklist_data'], true);
        if ($data) {
            $totalItems = 0;
            foreach ($data as $category => $items) {
                $totalItems += count($items);
            }
            echo "  - Total items: $totalItems\n";
        } else {
            echo "  - ❌ Invalid JSON data\n";
        }
    }
    echo "\n";
}

try {
    // Step 1: Initial state
    checkDatabaseState($db, $projectId, "Initial");
    
    // Step 2: Get checklist using model
    echo "🔄 Getting checklist using model...\n";
    $checklist = $clientProject->getProjectChecklist($projectId);
    echo "Model returned " . count($checklist) . " checklists\n\n";
    checkDatabaseState($db, $projectId, "After model call");
    
    // Step 3: Parse data and find test item
    if (!empty($checklist)) {
        $checklistData = json_decode($checklist[0]['checklist_data'], true);
        $testItem = null;
        foreach ($checklistData as $category => $items) {
            if (!empty($items)) {
                $testItem = $items[0];
                break;
            }
        }
        
        if ($testItem) {
            echo "🎯 Found test item: " . $testItem['id'] . " - " . $testItem['title'] . "\n\n";
            
            // Step 4: Test update operation
            echo "🔄 Testing update operation...\n";
            $originalStatus = $testItem['completed'] ?? false;
            $newStatus = !$originalStatus;
            
            echo "  - Original status: " . ($originalStatus ? 'completed' : 'not completed') . "\n";
            echo "  - New status: " . ($newStatus ? 'completed' : 'not completed') . "\n";
            
            $result = $clientProject->updateChecklistItem($projectId, $testItem['id'], $newStatus);
            echo "  - Update result: " . ($result ? 'success' : 'failed') . "\n\n";
            
            checkDatabaseState($db, $projectId, "After update");
            
            // Step 5: Test getting checklist again
            echo "🔄 Getting checklist again...\n";
            $checklist2 = $clientProject->getProjectChecklist($projectId);
            echo "Second model call returned " . count($checklist2) . " checklists\n\n";
            checkDatabaseState($db, $projectId, "After second model call");
            
            // Step 6: Revert the change
            if ($result && !empty($checklist2)) {
                echo "🔄 Reverting change...\n";
                $revertResult = $clientProject->updateChecklistItem($projectId, $testItem['id'], $originalStatus);
                echo "  - Revert result: " . ($revertResult ? 'success' : 'failed') . "\n\n";
                checkDatabaseState($db, $projectId, "After revert");
            }
        } else {
            echo "❌ No test item found\n";
        }
    } else {
        echo "❌ No checklist found\n";
    }
    
    // Step 7: Test database connection methods
    echo "🔧 Testing database connection methods...\n";
    echo "  - isConnectionHealthy(): " . ($db->isConnectionHealthy() ? 'true' : 'false') . "\n";
    
    // Test a simple query
    $testQuery = $db->fetch('SELECT COUNT(*) as count FROM project_checklists WHERE project_id = ?', [$projectId]);
    echo "  - Test query result: " . ($testQuery ? $testQuery['count'] : 'failed') . "\n";
    
    // Test reconnection
    echo "  - Testing reconnection...\n";
    $reconnectResult = $db->reconnect();
    echo "  - Reconnect result: " . ($reconnectResult ? 'success' : 'failed') . "\n";
    
    // Test query after reconnection
    $testQuery2 = $db->fetch('SELECT COUNT(*) as count FROM project_checklists WHERE project_id = ?', [$projectId]);
    echo "  - Test query after reconnect: " . ($testQuery2 ? $testQuery2['count'] : 'failed') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
