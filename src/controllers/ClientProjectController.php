<?php
/**
 * Client Project Controller
 *
 * Handles client project management with organized structure:
 * 📁 Client Projects → 🌐 Website – [Client Name]
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Project.php';
require_once __DIR__ . '/../models/FreelanceClient.php';
require_once __DIR__ . '/../models/ClientProject.php';
require_once __DIR__ . '/../models/Task.php';
require_once __DIR__ . '/../utils/Session.php';

class ClientProjectController extends BaseController {
    private $projectModel;
    private $clientModel;
    private $clientProjectModel;
    private $taskModel;

    public function __construct() {
        $this->projectModel = new Project();
        $this->clientModel = new FreelanceClient();
        $this->clientProjectModel = new ClientProject();
        $this->taskModel = new Task();
    }

    /**
     * Show client projects dashboard with organized structure
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get client projects organized by client
        $clientProjects = $this->clientProjectModel->getClientProjectsGrouped($userId, $filters);

        // Get clients for filter dropdown
        $clients = $this->clientModel->getUserClients($userId);

        // Get project summary statistics
        $projectSummary = $this->clientProjectModel->getClientProjectSummary($userId);

        $this->view('client-projects/index', [
            'clientProjects' => $clientProjects,
            'clients' => $clients,
            'projectSummary' => $projectSummary,
            'filters' => $filters
        ]);
    }

    /**
     * Show create client project form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get clients for dropdown
        $clients = $this->clientModel->getUserClients($userId);

        // Get project templates
        $templates = $this->projectModel->getProjectTemplates($userId);

        // Get checklist templates
        $checklistTemplates = $this->clientProjectModel->getChecklistTemplates($userId);

        $this->view('client-projects/create', [
            'clients' => $clients,
            'templates' => $templates,
            'checklistTemplates' => $checklistTemplates
        ]);
    }

    /**
     * Store new client project
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['name']) || empty($data['client_id'])) {
            Session::setFlash('error', 'Project name and client are required');
            $this->redirect('/client-projects/create');
        }

        try {
            // Check if using Notion template
            if (!empty($data['use_notion_template']) && $data['use_notion_template'] === '1') {
                $projectId = $this->clientProjectModel->createProjectFromNotionTemplate($userId, $data);
            } else {
                $projectId = $this->clientProjectModel->createClientProject($userId, $data);
            }

            if ($projectId) {
                Session::setFlash('success', 'Client project created successfully');
                $this->redirect('/client-projects/view/' . $projectId);
            } else {
                throw new Exception('Failed to create project');
            }
        } catch (Exception $e) {
            Session::setFlash('error', 'Error creating project: ' . $e->getMessage());
            $this->redirect('/client-projects/create');
        }
    }

    /**
     * Show client project master page with organized sections
     */
    public function viewProject($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project details
        $project = $this->clientProjectModel->getClientProjectDetails($id, $userId);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        // Get project sections data
        $projectData = [
            'project' => $project,
            'checklist' => $this->clientProjectModel->getProjectChecklist($id),
            'assets' => $this->clientProjectModel->getProjectAssets($id),
            'meetingNotes' => $this->clientProjectModel->getProjectMeetingNotes($id),
            'contentPlan' => $this->clientProjectModel->getProjectContentPlan($id),
            'milestones' => $this->clientProjectModel->getProjectMilestones($id),
            'timeline' => $this->clientProjectModel->getProjectTimeline($id),
            'tasks' => $this->taskModel->getProjectTasks($id),
            'invoices' => $this->clientProjectModel->getProjectInvoices($id)
        ];

        $this->view('client-projects/view', $projectData);
    }

    /**
     * Show project details section
     */
    public function projectDetails($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->getClientProjectDetails($id, $userId);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        $this->view('client-projects/sections/project-details', [
            'project' => $project
        ]);
    }

    /**
     * Show and manage project checklist
     */
    public function checklist($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->getClientProjectDetails($id, $userId);
        $checklist = $this->clientProjectModel->getProjectChecklist($id);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        $this->view('client-projects/sections/checklist', [
            'project' => $project,
            'checklist' => $checklist
        ]);
    }

    /**
     * Update checklist item status
     */
    public function updateChecklistItem() {
        $this->requireLogin();

        $data = $this->getPostData();

        if (empty($data['project_id']) || empty($data['item_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Missing required data']);
            return;
        }

        try {
            $result = $this->clientProjectModel->updateChecklistItem(
                $data['project_id'],
                $data['item_id'],
                $data['completed'] ?? false
            );

            $this->jsonResponse(['success' => $result]);
        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Edit checklist item with enhanced error handling
     */
    public function editChecklistItem() {
        $this->requireLogin();

        $data = $this->getPostData();
        $startTime = microtime(true);

        // Log the incoming request for debugging
        error_log("Edit checklist item request: " . json_encode([
            'project_id' => $data['project_id'] ?? 'missing',
            'item_id' => $data['item_id'] ?? 'missing',
            'title' => $data['title'] ?? 'missing',
            'timestamp' => date('Y-m-d H:i:s')
        ]));

        if (empty($data['project_id']) || empty($data['item_id']) || empty($data['title'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Missing required data']);
            return;
        }

        try {
            $result = $this->clientProjectModel->editChecklistItem(
                $data['project_id'],
                $data['item_id'],
                [
                    'title' => $data['title'],
                    'description' => $data['description'] ?? '',
                    'category' => $data['category'] ?? '',
                    'completed' => $data['completed'] ?? false
                ]
            );

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            error_log("Edit checklist item completed successfully in {$executionTime}ms");

            $this->jsonResponse(['success' => $result]);
        } catch (Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);

            // Enhanced error logging with context
            error_log("Edit checklist item failed after {$executionTime}ms: " . json_encode([
                'error' => $e->getMessage(),
                'project_id' => $data['project_id'],
                'item_id' => $data['item_id'],
                'trace' => $e->getTraceAsString()
            ]));

            // Provide user-friendly error messages
            $userMessage = $e->getMessage();
            if (strpos($e->getMessage(), 'Deadlock') !== false) {
                $userMessage = 'Operation failed due to concurrent access. Please try again.';
            } elseif (strpos($e->getMessage(), 'connection') !== false) {
                $userMessage = 'Database connection issue. Please try again.';
            } elseif (strpos($e->getMessage(), 'Checklist not found') !== false) {
                $userMessage = 'Checklist not found. Please refresh the page and try again.';
            }

            $this->jsonResponse(['success' => false, 'message' => $userMessage]);
        }
    }

    /**
     * Add new checklist item
     */
    public function addChecklistItem() {
        $this->requireLogin();

        $data = $this->getPostData();

        if (empty($data['project_id']) || empty($data['title'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Missing required data']);
            return;
        }

        try {
            $result = $this->clientProjectModel->addChecklistItem(
                $data['project_id'],
                [
                    'title' => $data['title'],
                    'description' => $data['description'] ?? '',
                    'category' => $data['category'] ?? 'General'
                ]
            );

            $this->jsonResponse(['success' => $result]);
        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Delete checklist item
     */
    public function deleteChecklistItem() {
        $this->requireLogin();

        $data = $this->getPostData();

        if (empty($data['project_id']) || empty($data['item_id'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Missing required data']);
            return;
        }

        try {
            $result = $this->clientProjectModel->deleteChecklistItem(
                $data['project_id'],
                $data['item_id']
            );

            $this->jsonResponse(['success' => $result]);
        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Show project assets section
     */
    public function assets($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->getClientProjectDetails($id, $userId);
        $assets = $this->clientProjectModel->getProjectAssets($id);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        $this->view('client-projects/sections/assets', [
            'project' => $project,
            'assets' => $assets
        ]);
    }

    /**
     * Upload project asset
     */
    public function uploadAsset($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $result = $this->clientProjectModel->uploadProjectAsset($id, $userId, $_FILES, $_POST);
                
                if ($result) {
                    Session::setFlash('success', 'Asset uploaded successfully');
                } else {
                    Session::setFlash('error', 'Failed to upload asset');
                }
            } catch (Exception $e) {
                Session::setFlash('error', 'Error uploading asset: ' . $e->getMessage());
            }
        }

        $this->redirect('/client-projects/assets/' . $id);
    }

    /**
     * Show meeting notes section
     */
    public function meetingNotes($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->getClientProjectDetails($id, $userId);
        $meetingNotes = $this->clientProjectModel->getProjectMeetingNotes($id);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        $this->view('client-projects/sections/meeting-notes', [
            'project' => $project,
            'meetingNotes' => $meetingNotes
        ]);
    }

    /**
     * Add meeting note
     */
    public function addMeetingNote($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = $this->getPostData();
            
            try {
                $result = $this->clientProjectModel->addMeetingNote($id, $userId, $data);
                
                if ($result) {
                    Session::setFlash('success', 'Meeting note added successfully');
                } else {
                    Session::setFlash('error', 'Failed to add meeting note');
                }
            } catch (Exception $e) {
                Session::setFlash('error', 'Error adding meeting note: ' . $e->getMessage());
            }
        }

        $this->redirect('/client-projects/meeting-notes/' . $id);
    }

    /**
     * Show content plan section
     */
    public function contentPlan($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->getClientProjectDetails($id, $userId);
        $contentPlan = $this->clientProjectModel->getProjectContentPlan($id);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        $this->view('client-projects/sections/content-plan', [
            'project' => $project,
            'contentPlan' => $contentPlan
        ]);
    }

    /**
     * Show timeline section
     */
    public function timeline($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->getClientProjectDetails($id, $userId);
        $milestones = $this->clientProjectModel->getProjectMilestones($id);
        $timeline = $this->clientProjectModel->getProjectTimeline($id);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        $this->view('client-projects/sections/timeline', [
            'project' => $project,
            'milestones' => $milestones,
            'timeline' => $timeline
        ]);
    }

    /**
     * Edit client project
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->getClientProjectDetails($id, $userId);

        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        // Get clients for dropdown
        $clients = $this->clientModel->getUserClients($userId);

        $this->view('client-projects/edit', [
            'project' => $project,
            'clients' => $clients
        ]);
    }

    /**
     * Update client project
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->find($id);

        if (!$project || $project['user_id'] != $userId) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['name']) || empty($data['client_id'])) {
            Session::setFlash('error', 'Project name and client are required');
            $this->redirect('/client-projects/edit/' . $id);
        }

        try {
            $updateData = [
                'client_id' => $data['client_id'],
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'start_date' => $data['start_date'] ?? null,
                'end_date' => $data['end_date'] ?? null,
                'status' => $data['status'] ?? $project['status'],
                'client_project_category' => $data['client_project_category'] ?? $project['client_project_category'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = $this->clientProjectModel->update($id, $updateData);

            if ($result) {
                Session::setFlash('success', 'Project updated successfully');
                $this->redirect('/client-projects/view/' . $id);
            } else {
                throw new Exception('Failed to update project');
            }
        } catch (Exception $e) {
            Session::setFlash('error', 'Error updating project: ' . $e->getMessage());
            $this->redirect('/client-projects/edit/' . $id);
        }
    }

    /**
     * Delete client project
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $project = $this->clientProjectModel->find($id);

        if (!$project || $project['user_id'] != $userId) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/client-projects');
        }

        try {
            $result = $this->clientProjectModel->delete($id);

            if ($result) {
                Session::setFlash('success', 'Project deleted successfully');
            } else {
                Session::setFlash('error', 'Failed to delete project');
            }
        } catch (Exception $e) {
            Session::setFlash('error', 'Error deleting project: ' . $e->getMessage());
        }

        $this->redirect('/client-projects');
    }

    /**
     * JSON response helper
     */
    protected function jsonResponse($data, $statusCode = 200) {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}
