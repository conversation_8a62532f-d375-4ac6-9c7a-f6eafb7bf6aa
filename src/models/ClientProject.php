<?php
/**
 * Client Project Model
 *
 * Handles client project-related database operations with enhanced features
 * for organized project management.
 */

require_once __DIR__ . '/BaseModel.php';

class ClientProject extends BaseModel {
    protected $table = 'projects';
    protected $assetsTable = 'project_assets';
    protected $meetingNotesTable = 'project_meeting_notes';
    protected $contentPlansTable = 'project_content_plans';
    protected $milestonesTable = 'project_milestones';
    protected $checklistsTable = 'project_checklists';
    protected $checklistTemplatesTable = 'project_checklist_templates';
    protected $timelineEventsTable = 'project_timeline_events';

    /**
     * Get client projects grouped by client
     */
    public function getClientProjectsGrouped($userId, $filters = []) {
        $sql = "SELECT p.*,
                c.name as client_name,
                c.company as client_company,
                COUNT(DISTINCT t.id) as total_tasks,
                COUNT(DISTINCT CASE WHEN t.status = 'done' THEN t.id END) as completed_tasks,
                COUNT(DISTINCT m.id) as total_milestones,
                COUNT(DISTINCT CASE WHEN m.status = 'completed' THEN m.id END) as completed_milestones
                FROM {$this->table} p
                LEFT JOIN freelance_clients c ON p.client_id = c.id
                LEFT JOIN tasks t ON p.id = t.project_id
                LEFT JOIN {$this->milestonesTable} m ON p.id = m.project_id
                WHERE p.user_id = ? AND p.project_type = 'client' ";

        $params = [$userId];

        // Apply filters
        if (!empty($filters['client_id'])) {
            $sql .= " AND p.client_id = ? ";
            $params[] = $filters['client_id'];
        }

        if (!empty($filters['status'])) {
            $sql .= " AND p.status = ? ";
            $params[] = $filters['status'];
        }

        if (!empty($filters['category'])) {
            $sql .= " AND p.client_project_category = ? ";
            $params[] = $filters['category'];
        }

        $sql .= " GROUP BY p.id, c.id ORDER BY c.name ASC, p.created_at DESC";

        $projects = $this->db->fetchAll($sql, $params);

        // Ensure projects is an array
        if (!is_array($projects)) {
            $projects = [];
        }

        // Group by client
        $groupedProjects = [];
        foreach ($projects as $project) {
            $clientKey = $project['client_id'] ?? 'no_client';
            if (!isset($groupedProjects[$clientKey])) {
                $groupedProjects[$clientKey] = [
                    'client' => [
                        'id' => $project['client_id'],
                        'name' => $project['client_name'],
                        'company' => $project['client_company']
                    ],
                    'projects' => []
                ];
            }
            $groupedProjects[$clientKey]['projects'][] = $project;
        }

        return $groupedProjects;
    }

    /**
     * Get client project summary statistics
     */
    public function getClientProjectSummary($userId) {
        $sql = "SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_projects,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
                COUNT(CASE WHEN status = 'planning' THEN 1 END) as planning_projects,
                COUNT(DISTINCT client_id) as total_clients
                FROM {$this->table} 
                WHERE user_id = ? AND project_type = 'client'";

        return $this->db->fetch($sql, [$userId]);
    }

    /**
     * Create a new client project with initial setup
     */
    public function createClientProject($userId, $data) {
        try {
            $this->db->beginTransaction();

            // Prepare project data
            $projectData = [
                'user_id' => $userId,
                'client_id' => $data['client_id'],
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'start_date' => $data['start_date'] ?? date('Y-m-d'),
                'end_date' => $data['end_date'] ?? null,
                'status' => 'planning',
                'project_type' => 'client',
                'client_project_category' => $data['category'] ?? 'Website',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Create project
            $projectId = $this->create($projectData);

            if (!$projectId) {
                throw new Exception("Failed to create project");
            }

            // Create initial checklist if template selected
            if (!empty($data['checklist_template_id'])) {
                $this->createProjectChecklistFromTemplate($projectId, $data['checklist_template_id'], $userId);
            }

            // Create initial milestones if provided
            if (!empty($data['milestones'])) {
                $this->createInitialMilestones($projectId, $userId, $data['milestones']);
            }

            $this->db->commit();
            return $projectId;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Get detailed client project information
     */
    public function getClientProjectDetails($projectId, $userId) {
        $sql = "SELECT p.*,
                c.name as client_name,
                c.company as client_company,
                c.email as client_email,
                c.phone as client_phone,
                c.website as client_website,
                COUNT(DISTINCT t.id) as total_tasks,
                COUNT(DISTINCT CASE WHEN t.status = 'done' THEN t.id END) as completed_tasks,
                COUNT(DISTINCT m.id) as total_milestones,
                COUNT(DISTINCT CASE WHEN m.status = 'completed' THEN m.id END) as completed_milestones,
                COUNT(DISTINCT a.id) as total_assets
                FROM {$this->table} p
                LEFT JOIN freelance_clients c ON p.client_id = c.id
                LEFT JOIN tasks t ON p.id = t.project_id
                LEFT JOIN {$this->milestonesTable} m ON p.id = m.project_id
                LEFT JOIN {$this->assetsTable} a ON p.id = a.project_id
                WHERE p.id = ? AND p.user_id = ? AND p.project_type = 'client'
                GROUP BY p.id";

        return $this->db->fetch($sql, [$projectId, $userId]);
    }

    /**
     * Get project checklist
     */
    public function getProjectChecklist($projectId) {
        $sql = "SELECT * FROM {$this->checklistsTable} 
                WHERE project_id = ? 
                ORDER BY created_at ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get project assets organized by category
     */
    public function getProjectAssets($projectId) {
        $sql = "SELECT * FROM {$this->assetsTable} 
                WHERE project_id = ? 
                ORDER BY asset_category ASC, created_at DESC";

        $assets = $this->db->fetchAll($sql, [$projectId]);

        // Group by category
        $groupedAssets = [];
        foreach ($assets as $asset) {
            $category = $asset['asset_category'];
            if (!isset($groupedAssets[$category])) {
                $groupedAssets[$category] = [];
            }
            $groupedAssets[$category][] = $asset;
        }

        return $groupedAssets;
    }

    /**
     * Get project meeting notes
     */
    public function getProjectMeetingNotes($projectId) {
        $sql = "SELECT * FROM {$this->meetingNotesTable} 
                WHERE project_id = ? 
                ORDER BY meeting_date DESC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get project content plan
     */
    public function getProjectContentPlan($projectId) {
        $sql = "SELECT * FROM {$this->contentPlansTable} 
                WHERE project_id = ? 
                ORDER BY priority DESC, due_date ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get project milestones
     */
    public function getProjectMilestones($projectId) {
        $sql = "SELECT * FROM {$this->milestonesTable} 
                WHERE project_id = ? 
                ORDER BY due_date ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get project timeline events
     */
    public function getProjectTimeline($projectId) {
        $sql = "SELECT * FROM {$this->timelineEventsTable} 
                WHERE project_id = ? 
                ORDER BY event_date ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get project invoices (from freelance system)
     */
    public function getProjectInvoices($projectId) {
        // Get project details first to get client_id
        $project = $this->find($projectId);
        if (!$project || !$project['client_id']) {
            return [];
        }

        $sql = "SELECT * FROM freelance_invoices 
                WHERE client_id = ? 
                ORDER BY created_at DESC";

        return $this->db->fetchAll($sql, [$project['client_id']]);
    }

    /**
     * Get checklist templates
     */
    public function getChecklistTemplates($userId) {
        $sql = "SELECT * FROM {$this->checklistTemplatesTable} 
                WHERE user_id = ? OR is_default = 1 
                ORDER BY is_default DESC, template_name ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Create project checklist from template
     */
    public function createProjectChecklistFromTemplate($projectId, $templateId, $userId) {
        // Get template
        $template = $this->db->fetch(
            "SELECT * FROM {$this->checklistTemplatesTable} WHERE id = ?",
            [$templateId]
        );

        if (!$template) {
            throw new Exception("Checklist template not found");
        }

        // Create checklist instance
        $checklistData = [
            'project_id' => $projectId,
            'template_id' => $templateId,
            'user_id' => $userId,
            'checklist_name' => $template['template_name'],
            'checklist_data' => $template['checklist_data'],
            'completion_percentage' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->insert($this->checklistsTable, $checklistData);
    }

    /**
     * Update checklist item status with retry logic
     */
    public function updateChecklistItem($projectId, $itemId, $completed) {
        return $this->executeWithRetry(function() use ($projectId, $itemId, $completed) {
            return $this->performUpdateChecklistItem($projectId, $itemId, $completed);
        });
    }

    /**
     * Perform the actual update checklist item operation
     */
    private function performUpdateChecklistItem($projectId, $itemId, $completed) {
        try {
            // Validate connection before starting
            if (!$this->db->isConnectionHealthy()) {
                if (!$this->db->reconnect()) {
                    throw new Exception("Database connection is not available");
                }
            }

            // Start transaction for atomic operation
            $this->db->beginTransaction();

            // Get current checklist with row lock
            $checklist = $this->db->fetch(
                "SELECT * FROM {$this->checklistsTable} WHERE project_id = ? FOR UPDATE",
                [$projectId]
            );

            if (!$checklist) {
                throw new Exception("Checklist not found");
            }

            // Update checklist data
            $checklistData = json_decode($checklist['checklist_data'], true);
            $updated = false;

            // Find and update the item
            foreach ($checklistData as $categoryName => &$items) {
                foreach ($items as &$item) {
                    if ($item['id'] === $itemId) {
                        $item['completed'] = $completed;
                        if ($completed) {
                            $item['completed_at'] = date('Y-m-d H:i:s');
                        } else {
                            unset($item['completed_at']);
                        }
                        $updated = true;
                        break 2;
                    }
                }
            }

            if (!$updated) {
                throw new Exception("Checklist item not found");
            }

            // Calculate completion percentage
            $completionPercentage = $this->calculateCompletionPercentage($checklistData);

            // Update checklist in database
            $result = $this->db->update(
                $this->checklistsTable,
                [
                    'checklist_data' => json_encode($checklistData),
                    'completion_percentage' => $completionPercentage,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                'project_id = ?',
                [$projectId]
            );

            if (!$result) {
                throw new Exception("Failed to update checklist data");
            }

            // Commit transaction
            $this->db->commit();

            // Log successful operation
            error_log("Successfully updated checklist item $itemId status for project $projectId");

            return $result;

        } catch (Exception $e) {
            // Rollback transaction on any error
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                error_log("Failed to rollback transaction: " . $rollbackException->getMessage());
            }

            // Log the error with context
            error_log("Failed to update checklist item $itemId status for project $projectId: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Execute a database operation with retry logic
     */
    private function executeWithRetry($operation, $maxRetries = 3, $baseDelay = 100) {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxRetries) {
            try {
                return $operation();
            } catch (Exception $e) {
                $lastException = $e;
                $attempt++;

                // Check if this is a retryable error
                if (!$this->isRetryableError($e) || $attempt >= $maxRetries) {
                    throw $e;
                }

                // Log the retry attempt
                error_log("Retry attempt $attempt for operation due to: " . $e->getMessage());

                // Handle connection errors by reconnecting
                if ($this->db->isConnectionError($e)) {
                    if (!$this->db->reconnect()) {
                        throw new Exception("Failed to reconnect to database after connection error");
                    }
                }

                // Exponential backoff with jitter
                $delay = $baseDelay * pow(2, $attempt - 1) + rand(0, 50);
                usleep($delay * 1000); // Convert to microseconds
            }
        }

        throw $lastException;
    }

    /**
     * Check if an error is retryable
     */
    private function isRetryableError($exception) {
        // Retry on deadlocks and connection errors
        return $this->db->isDeadlockError($exception) ||
               $this->db->isConnectionError($exception);
    }

    /**
     * Edit checklist item with retry logic
     */
    public function editChecklistItem($projectId, $itemId, $itemData) {
        return $this->executeWithRetry(function() use ($projectId, $itemId, $itemData) {
            return $this->performEditChecklistItem($projectId, $itemId, $itemData);
        });
    }

    /**
     * Perform the actual checklist item edit operation
     */
    private function performEditChecklistItem($projectId, $itemId, $itemData) {
        try {
            // Validate connection before starting
            if (!$this->db->isConnectionHealthy()) {
                if (!$this->db->reconnect()) {
                    throw new Exception("Database connection is not available");
                }
            }

            // Start transaction for atomic operation
            $this->db->beginTransaction();

            // Get current checklist with row lock to prevent concurrent modifications
            $checklist = $this->db->fetch(
                "SELECT * FROM {$this->checklistsTable} WHERE project_id = ? FOR UPDATE",
                [$projectId]
            );

            if (!$checklist) {
                throw new Exception("Checklist not found");
            }

            // Parse current checklist data
            $checklistData = json_decode($checklist['checklist_data'], true);
            if (!$checklistData) {
                throw new Exception("Invalid checklist data");
            }

            // Create new checklist data with the updated item
            $newChecklistData = $this->updateItemInChecklist($checklistData, $itemId, $itemData);

            // Calculate new completion percentage
            $completionPercentage = $this->calculateCompletionPercentage($newChecklistData);

            // Save updated checklist
            $result = $this->saveChecklistData($projectId, $newChecklistData, $completionPercentage);

            if (!$result) {
                throw new Exception("Failed to save checklist data");
            }

            // Commit transaction
            $this->db->commit();

            // Log successful operation
            error_log("Successfully edited checklist item $itemId for project $projectId");

            return true;

        } catch (Exception $e) {
            // Rollback transaction on any error
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                error_log("Failed to rollback transaction: " . $rollbackException->getMessage());
            }

            // Log the error with context
            error_log("Failed to edit checklist item $itemId for project $projectId: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update a specific item in the checklist data
     */
    private function updateItemInChecklist($checklistData, $itemId, $itemData) {
        $newChecklistData = [];
        $itemFound = false;
        $updatedItem = null;

        // First pass: Find and update the item, collect all other items
        foreach ($checklistData as $categoryName => $items) {
            $newChecklistData[$categoryName] = [];

            foreach ($items as $item) {
                if ($item['id'] === $itemId && !$itemFound) {
                    // Found the item to update
                    $updatedItem = [
                        'id' => $itemId,
                        'title' => $itemData['title'],
                        'description' => $itemData['description'] ?? '',
                        'completed' => $itemData['completed'] ?? false
                    ];

                    // Add completion timestamp if needed
                    if ($updatedItem['completed'] && !($item['completed'] ?? false)) {
                        $updatedItem['completed_at'] = date('Y-m-d H:i:s');
                    } elseif (!$updatedItem['completed'] && isset($item['completed_at'])) {
                        // Remove completion timestamp if uncompleted
                        unset($updatedItem['completed_at']);
                    } elseif (isset($item['completed_at'])) {
                        // Preserve existing completion timestamp
                        $updatedItem['completed_at'] = $item['completed_at'];
                    }

                    $itemFound = true;

                    // Add to target category (might be different from current category)
                    $targetCategory = $itemData['category'];
                    if (!isset($newChecklistData[$targetCategory])) {
                        $newChecklistData[$targetCategory] = [];
                    }
                    $newChecklistData[$targetCategory][] = $updatedItem;
                } else {
                    // Keep other items as they are
                    $newChecklistData[$categoryName][] = $item;
                }
            }
        }

        if (!$itemFound) {
            throw new Exception("Item with ID '$itemId' not found");
        }

        // Clean up empty categories
        foreach ($newChecklistData as $categoryName => $items) {
            if (empty($items)) {
                unset($newChecklistData[$categoryName]);
            }
        }

        return $newChecklistData;
    }

    /**
     * Calculate completion percentage for checklist data
     */
    private function calculateCompletionPercentage($checklistData) {
        $totalItems = 0;
        $completedItems = 0;

        foreach ($checklistData as $items) {
            foreach ($items as $item) {
                $totalItems++;
                if ($item['completed'] ?? false) {
                    $completedItems++;
                }
            }
        }

        return $totalItems > 0 ? round(($completedItems / $totalItems) * 100) : 0;
    }

    /**
     * Save checklist data to database
     */
    private function saveChecklistData($projectId, $checklistData, $completionPercentage) {
        $jsonData = json_encode($checklistData);

        if ($jsonData === false) {
            throw new Exception("Failed to encode checklist data to JSON: " . json_last_error_msg());
        }

        return $this->db->update(
            $this->checklistsTable,
            [
                'checklist_data' => $jsonData,
                'completion_percentage' => $completionPercentage,
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'project_id = ?',
            [$projectId]
        );
    }

    /**
     * Add new checklist item with retry logic
     */
    public function addChecklistItem($projectId, $itemData) {
        return $this->executeWithRetry(function() use ($projectId, $itemData) {
            return $this->performAddChecklistItem($projectId, $itemData);
        });
    }

    /**
     * Perform the actual add checklist item operation
     */
    private function performAddChecklistItem($projectId, $itemData) {
        try {
            // Validate connection before starting
            if (!$this->db->isConnectionHealthy()) {
                if (!$this->db->reconnect()) {
                    throw new Exception("Database connection is not available");
                }
            }

            // Start transaction for atomic operation
            $this->db->beginTransaction();

            // Get current checklist with row lock
            $checklist = $this->db->fetch(
                "SELECT * FROM {$this->checklistsTable} WHERE project_id = ? FOR UPDATE",
                [$projectId]
            );

            if (!$checklist) {
                throw new Exception("Checklist not found");
            }

            // Parse current checklist data
            $checklistData = json_decode($checklist['checklist_data'], true);
            if (!$checklistData) {
                $checklistData = [];
            }

            // Create new item
            $newItem = [
                'id' => uniqid(),
                'title' => $itemData['title'],
                'description' => $itemData['description'] ?? '',
                'completed' => false
            ];

            // Add to specified category or create new category
            $category = $itemData['category'] ?? 'General';
            if (!isset($checklistData[$category])) {
                $checklistData[$category] = [];
            }

            $checklistData[$category][] = $newItem;

            // Calculate completion percentage and save
            $completionPercentage = $this->calculateCompletionPercentage($checklistData);
            $result = $this->saveChecklistData($projectId, $checklistData, $completionPercentage);

            if (!$result) {
                throw new Exception("Failed to save checklist data");
            }

            // Commit transaction
            $this->db->commit();

            // Log successful operation
            error_log("Successfully added checklist item to project $projectId");

            return $result;

        } catch (Exception $e) {
            // Rollback transaction on any error
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                error_log("Failed to rollback transaction: " . $rollbackException->getMessage());
            }

            // Log the error with context
            error_log("Failed to add checklist item to project $projectId: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete checklist item with retry logic
     */
    public function deleteChecklistItem($projectId, $itemId) {
        return $this->executeWithRetry(function() use ($projectId, $itemId) {
            return $this->performDeleteChecklistItem($projectId, $itemId);
        });
    }

    /**
     * Perform the actual delete checklist item operation
     */
    private function performDeleteChecklistItem($projectId, $itemId) {
        try {
            // Validate connection before starting
            if (!$this->db->isConnectionHealthy()) {
                if (!$this->db->reconnect()) {
                    throw new Exception("Database connection is not available");
                }
            }

            // Start transaction for atomic operation
            $this->db->beginTransaction();

            // Get current checklist with row lock
            $checklist = $this->db->fetch(
                "SELECT * FROM {$this->checklistsTable} WHERE project_id = ? FOR UPDATE",
                [$projectId]
            );

            if (!$checklist) {
                throw new Exception("Checklist not found");
            }

            // Parse current checklist data
            $checklistData = json_decode($checklist['checklist_data'], true);
            if (!$checklistData) {
                throw new Exception("Invalid checklist data");
            }

            // Create new checklist data without the deleted item
            $newChecklistData = $this->removeItemFromChecklist($checklistData, $itemId);

            // Calculate completion percentage and save
            $completionPercentage = $this->calculateCompletionPercentage($newChecklistData);
            $result = $this->saveChecklistData($projectId, $newChecklistData, $completionPercentage);

            if (!$result) {
                throw new Exception("Failed to save checklist data");
            }

            // Commit transaction
            $this->db->commit();

            // Log successful operation
            error_log("Successfully deleted checklist item $itemId from project $projectId");

            return $result;

        } catch (Exception $e) {
            // Rollback transaction on any error
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                error_log("Failed to rollback transaction: " . $rollbackException->getMessage());
            }

            // Log the error with context
            error_log("Failed to delete checklist item $itemId from project $projectId: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Remove a specific item from the checklist data
     */
    private function removeItemFromChecklist($checklistData, $itemId) {
        $newChecklistData = [];
        $itemFound = false;

        foreach ($checklistData as $categoryName => $items) {
            $newChecklistData[$categoryName] = [];

            foreach ($items as $item) {
                if ($item['id'] === $itemId && !$itemFound) {
                    // Skip this item (delete it)
                    $itemFound = true;
                } else {
                    // Keep this item
                    $newChecklistData[$categoryName][] = $item;
                }
            }
        }

        if (!$itemFound) {
            throw new Exception("Item with ID '$itemId' not found");
        }

        // Clean up empty categories
        foreach ($newChecklistData as $categoryName => $items) {
            if (empty($items)) {
                unset($newChecklistData[$categoryName]);
            }
        }

        return $newChecklistData;
    }

    /**
     * Upload project asset
     */
    public function uploadProjectAsset($projectId, $userId, $files, $postData) {
        if (!isset($files['asset']) || $files['asset']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception("No file uploaded or upload error");
        }

        $file = $files['asset'];
        $uploadDir = __DIR__ . '/../../uploads/projects/' . $projectId . '/';
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception("Failed to move uploaded file");
        }

        // Save to database
        $assetData = [
            'project_id' => $projectId,
            'user_id' => $userId,
            'asset_category' => $postData['category'] ?? 'other',
            'file_name' => $filename,
            'original_name' => $file['name'],
            'file_path' => '/uploads/projects/' . $projectId . '/' . $filename,
            'file_type' => $file['type'],
            'file_size' => $file['size'],
            'description' => $postData['description'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->insert($this->assetsTable, $assetData);
    }

    /**
     * Add meeting note
     */
    public function addMeetingNote($projectId, $userId, $data) {
        $meetingData = [
            'project_id' => $projectId,
            'user_id' => $userId,
            'meeting_title' => $data['title'],
            'meeting_date' => $data['meeting_date'],
            'attendees' => $data['attendees'] ?? null,
            'agenda' => $data['agenda'] ?? null,
            'notes' => $data['notes'] ?? null,
            'action_items' => $data['action_items'] ?? null,
            'next_meeting_date' => $data['next_meeting_date'] ?? null,
            'meeting_type' => $data['meeting_type'] ?? 'progress',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->insert($this->meetingNotesTable, $meetingData);
    }

    /**
     * Create initial milestones
     */
    private function createInitialMilestones($projectId, $userId, $milestones) {
        foreach ($milestones as $milestone) {
            $milestoneData = [
                'project_id' => $projectId,
                'user_id' => $userId,
                'title' => $milestone['title'],
                'description' => $milestone['description'] ?? null,
                'due_date' => $milestone['due_date'],
                'milestone_type' => $milestone['type'] ?? 'other',
                'priority' => $milestone['priority'] ?? 'medium',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $sql = "INSERT INTO {$this->milestonesTable} (" . implode(', ', array_keys($milestoneData)) . ")
                    VALUES (" . str_repeat('?,', count($milestoneData) - 1) . "?)";

            $this->db->execute($sql, array_values($milestoneData));
        }
    }

    /**
     * Create project from Notion template
     */
    public function createProjectFromNotionTemplate($userId, $data) {
        try {
            $this->db->beginTransaction();

            // Create the main project
            $projectId = $this->createClientProject($userId, $data);

            if (!$projectId) {
                throw new Exception("Failed to create project");
            }

            // Add Notion-style default milestones
            $notionMilestones = [
                [
                    'title' => 'Project Setup & Planning Complete',
                    'description' => 'All planning activities and project setup completed',
                    'due_date' => date('Y-m-d', strtotime('+1 week')),
                    'type' => 'kickoff',
                    'priority' => 'high'
                ],
                [
                    'title' => 'Design & Branding Approved',
                    'description' => 'All design elements and branding completed and approved',
                    'due_date' => date('Y-m-d', strtotime('+3 weeks')),
                    'type' => 'design',
                    'priority' => 'high'
                ],
                [
                    'title' => 'Content Strategy Finalized',
                    'description' => 'All content created, reviewed, and approved',
                    'due_date' => date('Y-m-d', strtotime('+4 weeks')),
                    'type' => 'content',
                    'priority' => 'medium'
                ],
                [
                    'title' => 'Development Complete',
                    'description' => 'All development work finished and tested',
                    'due_date' => date('Y-m-d', strtotime('+6 weeks')),
                    'type' => 'development',
                    'priority' => 'high'
                ],
                [
                    'title' => 'Testing & QA Complete',
                    'description' => 'All testing completed and issues resolved',
                    'due_date' => date('Y-m-d', strtotime('+7 weeks')),
                    'type' => 'testing',
                    'priority' => 'high'
                ],
                [
                    'title' => 'Website Launch',
                    'description' => 'Website successfully launched and live',
                    'due_date' => date('Y-m-d', strtotime('+8 weeks')),
                    'type' => 'launch',
                    'priority' => 'critical'
                ]
            ];

            $this->createInitialMilestones($projectId, $userId, $notionMilestones);

            // Create initial content plan items
            $this->createNotionContentPlan($projectId, $userId);

            // Create initial meeting note template
            $this->createInitialMeetingNote($projectId, $userId, $data);

            $this->db->commit();
            return $projectId;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Create Notion-style content plan
     */
    private function createNotionContentPlan($projectId, $userId) {
        $contentItems = [
            [
                'content_type' => 'page',
                'title' => 'Homepage Content',
                'description' => 'Main homepage content including hero section, about, and call-to-action',
                'target_audience' => 'Primary website visitors',
                'status' => 'planned',
                'priority' => 'high'
            ],
            [
                'content_type' => 'page',
                'title' => 'About Page',
                'description' => 'Company or personal about page content',
                'target_audience' => 'Potential clients/customers',
                'status' => 'planned',
                'priority' => 'high'
            ],
            [
                'content_type' => 'page',
                'title' => 'Services/Products Page',
                'description' => 'Detailed descriptions of services or products offered',
                'target_audience' => 'Potential clients/customers',
                'status' => 'planned',
                'priority' => 'high'
            ],
            [
                'content_type' => 'page',
                'title' => 'Contact Page',
                'description' => 'Contact information and contact form',
                'target_audience' => 'All visitors',
                'status' => 'planned',
                'priority' => 'medium'
            ],
            [
                'content_type' => 'copy',
                'title' => 'SEO Meta Tags',
                'description' => 'Meta titles, descriptions, and keywords for all pages',
                'target_audience' => 'Search engines',
                'status' => 'planned',
                'priority' => 'medium'
            ]
        ];

        foreach ($contentItems as $item) {
            $contentData = [
                'project_id' => $projectId,
                'user_id' => $userId,
                'content_type' => $item['content_type'],
                'title' => $item['title'],
                'description' => $item['description'],
                'target_audience' => $item['target_audience'],
                'status' => $item['status'],
                'priority' => $item['priority'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->db->insert($this->contentPlansTable, $contentData);
        }
    }

    /**
     * Create initial meeting note
     */
    private function createInitialMeetingNote($projectId, $userId, $projectData) {
        $meetingData = [
            'project_id' => $projectId,
            'user_id' => $userId,
            'meeting_title' => 'Project Kickoff Meeting',
            'meeting_date' => date('Y-m-d H:i:s'),
            'attendees' => 'Project team and client stakeholders',
            'agenda' => "1. Project overview and objectives\n2. Timeline and milestones review\n3. Roles and responsibilities\n4. Communication plan\n5. Next steps",
            'notes' => "Project: " . $projectData['name'] . "\nClient: " . ($projectData['client_name'] ?? 'TBD') . "\n\nKey Discussion Points:\n- Project scope and requirements\n- Design preferences and brand guidelines\n- Content responsibilities\n- Technical requirements\n- Launch timeline",
            'action_items' => "1. Client to provide brand assets and content\n2. Schedule design review meeting\n3. Set up project communication channels\n4. Begin wireframe creation",
            'meeting_type' => 'kickoff',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $this->db->insert($this->meetingNotesTable, $meetingData);
    }
}
