<?php
/**
 * Test script for checklist AJAX endpoints
 * 
 * This script tests all the AJAX endpoints to identify which ones are failing
 */

require_once 'config/config.php';
require_once 'src/utils/Database.php';
require_once 'src/models/ClientProject.php';

class ChecklistAjaxTest {
    private $db;
    private $clientProject;
    private $testProjectId = 56;
    private $baseUrl = 'http://localhost/momentum';
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->clientProject = new ClientProject();
    }
    
    /**
     * Run all endpoint tests
     */
    public function runTests() {
        echo "🧪 Testing Checklist AJAX Endpoints\n";
        echo "===================================\n\n";
        
        try {
            $this->testGetChecklistData();
            $this->testUpdateItemEndpoint();
            $this->testEditItemEndpoint();
            $this->testAddItemEndpoint();
            $this->testDeleteItemEndpoint();
            
            echo "✅ All endpoint tests completed!\n";
            
        } catch (Exception $e) {
            echo "❌ Test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }
    
    /**
     * Test getting checklist data
     */
    private function testGetChecklistData() {
        echo "🔍 Testing checklist data retrieval...\n";
        
        $checklist = $this->clientProject->getProjectChecklist($this->testProjectId);
        if (empty($checklist)) {
            throw new Exception("No checklist found for project {$this->testProjectId}");
        }
        
        $checklistData = json_decode($checklist[0]['checklist_data'], true);
        if (!$checklistData) {
            throw new Exception("Invalid checklist JSON data");
        }
        
        // Find a test item
        $testItem = null;
        foreach ($checklistData as $category => $items) {
            if (!empty($items)) {
                $testItem = $items[0];
                break;
            }
        }
        
        if (!$testItem) {
            throw new Exception("No test item found in checklist");
        }
        
        echo "✅ Checklist data retrieved successfully\n";
        echo "  - Test item ID: " . $testItem['id'] . "\n";
        echo "  - Test item title: " . $testItem['title'] . "\n\n";
        
        return $testItem;
    }
    
    /**
     * Test update item endpoint (checkbox functionality)
     */
    private function testUpdateItemEndpoint() {
        echo "🔍 Testing update item endpoint...\n";
        
        $testItem = $this->testGetChecklistData();
        $originalStatus = $testItem['completed'] ?? false;
        $newStatus = !$originalStatus;
        
        // Test the model method directly
        try {
            $result = $this->clientProject->updateChecklistItem(
                $this->testProjectId,
                $testItem['id'],
                $newStatus
            );
            
            if ($result) {
                echo "✅ Update item model method works\n";
                
                // Revert the change
                $this->clientProject->updateChecklistItem(
                    $this->testProjectId,
                    $testItem['id'],
                    $originalStatus
                );
                echo "✅ Reverted change successfully\n";
            } else {
                echo "❌ Update item model method failed\n";
            }
        } catch (Exception $e) {
            echo "❌ Update item model method error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test edit item endpoint
     */
    private function testEditItemEndpoint() {
        echo "🔍 Testing edit item endpoint...\n";
        
        $testItem = $this->testGetChecklistData();
        $originalTitle = $testItem['title'];
        $newTitle = $originalTitle . " (Test Edit " . date('H:i:s') . ")";
        
        // Test the model method directly
        try {
            $result = $this->clientProject->editChecklistItem(
                $this->testProjectId,
                $testItem['id'],
                [
                    'title' => $newTitle,
                    'description' => $testItem['description'] ?? '',
                    'category' => 'General',
                    'completed' => $testItem['completed'] ?? false
                ]
            );
            
            if ($result) {
                echo "✅ Edit item model method works\n";
                
                // Revert the change
                $this->clientProject->editChecklistItem(
                    $this->testProjectId,
                    $testItem['id'],
                    [
                        'title' => $originalTitle,
                        'description' => $testItem['description'] ?? '',
                        'category' => 'General',
                        'completed' => $testItem['completed'] ?? false
                    ]
                );
                echo "✅ Reverted change successfully\n";
            } else {
                echo "❌ Edit item model method failed\n";
            }
        } catch (Exception $e) {
            echo "❌ Edit item model method error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test add item endpoint
     */
    private function testAddItemEndpoint() {
        echo "🔍 Testing add item endpoint...\n";
        
        $testTitle = "Test Item " . date('H:i:s');
        $addedItemId = null;
        
        // Test the model method directly
        try {
            $result = $this->clientProject->addChecklistItem(
                $this->testProjectId,
                [
                    'title' => $testTitle,
                    'description' => 'Test description',
                    'category' => 'General'
                ]
            );
            
            if ($result) {
                echo "✅ Add item model method works\n";
                
                // Find the added item to get its ID for cleanup
                $checklist = $this->clientProject->getProjectChecklist($this->testProjectId);
                $checklistData = json_decode($checklist[0]['checklist_data'], true);
                
                foreach ($checklistData as $category => $items) {
                    foreach ($items as $item) {
                        if ($item['title'] === $testTitle) {
                            $addedItemId = $item['id'];
                            break 2;
                        }
                    }
                }
                
                if ($addedItemId) {
                    // Clean up - delete the test item
                    $this->clientProject->deleteChecklistItem($this->testProjectId, $addedItemId);
                    echo "✅ Cleaned up test item successfully\n";
                } else {
                    echo "⚠️  Could not find added item for cleanup\n";
                }
            } else {
                echo "❌ Add item model method failed\n";
            }
        } catch (Exception $e) {
            echo "❌ Add item model method error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test delete item endpoint
     */
    private function testDeleteItemEndpoint() {
        echo "🔍 Testing delete item endpoint...\n";
        
        // First add a test item to delete
        $testTitle = "Test Item for Deletion " . date('H:i:s');
        
        try {
            // Add test item
            $result = $this->clientProject->addChecklistItem(
                $this->testProjectId,
                [
                    'title' => $testTitle,
                    'description' => 'Test item for deletion',
                    'category' => 'General'
                ]
            );
            
            if (!$result) {
                throw new Exception("Failed to add test item for deletion");
            }
            
            // Find the added item
            $checklist = $this->clientProject->getProjectChecklist($this->testProjectId);
            $checklistData = json_decode($checklist[0]['checklist_data'], true);
            $testItemId = null;
            
            foreach ($checklistData as $category => $items) {
                foreach ($items as $item) {
                    if ($item['title'] === $testTitle) {
                        $testItemId = $item['id'];
                        break 2;
                    }
                }
            }
            
            if (!$testItemId) {
                throw new Exception("Could not find test item for deletion");
            }
            
            // Now test deletion
            $deleteResult = $this->clientProject->deleteChecklistItem($this->testProjectId, $testItemId);
            
            if ($deleteResult) {
                echo "✅ Delete item model method works\n";
            } else {
                echo "❌ Delete item model method failed\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Delete item model method error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test HTTP endpoints with cURL
     */
    private function testHttpEndpoints() {
        echo "🔍 Testing HTTP endpoints...\n";
        
        // This would require setting up proper session/authentication
        // For now, we're testing the model methods directly
        echo "ℹ️  HTTP endpoint testing requires authentication setup\n";
        echo "   Model method testing covers the core functionality\n\n";
    }
}

// Run the tests
if (php_sapi_name() === 'cli') {
    $test = new ChecklistAjaxTest();
    $test->runTests();
} else {
    echo "<pre>";
    $test = new ChecklistAjaxTest();
    $test->runTests();
    echo "</pre>";
}
?>
