<?php
/**
 * Test script for checklist robustness with transaction support and retry logic
 * 
 * This script tests the enhanced checklist functionality to ensure:
 * 1. Single operations work correctly
 * 2. Sequential operations work reliably
 * 3. Retry logic handles failures gracefully
 * 4. Transaction support prevents data corruption
 */

require_once __DIR__ . '/src/config/config.php';
require_once __DIR__ . '/src/utils/Database.php';
require_once __DIR__ . '/src/models/ClientProject.php';

class ChecklistRobustnessTest {
    private $db;
    private $clientProject;
    private $testProjectId = 56; // Using the project ID from the context
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->clientProject = new ClientProject();
    }
    
    /**
     * Run all tests
     */
    public function runTests() {
        echo "🧪 Starting Checklist Robustness Tests\n";
        echo "=====================================\n\n";
        
        try {
            $this->testDatabaseConnection();
            $this->testSingleEdit();
            $this->testSequentialEdits();
            $this->testConcurrentOperations();
            $this->testRetryLogic();
            
            echo "✅ All tests completed successfully!\n";
            
        } catch (Exception $e) {
            echo "❌ Test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }
    
    /**
     * Test database connection health
     */
    private function testDatabaseConnection() {
        echo "🔍 Testing database connection health...\n";
        
        $isHealthy = $this->db->isConnectionHealthy();
        if (!$isHealthy) {
            throw new Exception("Database connection is not healthy");
        }
        
        echo "✅ Database connection is healthy\n\n";
    }
    
    /**
     * Test single edit operation
     */
    private function testSingleEdit() {
        echo "🔍 Testing single edit operation...\n";
        
        // Get current checklist to find an item to edit
        $checklist = $this->db->fetch(
            "SELECT * FROM project_checklists WHERE project_id = ?",
            [$this->testProjectId]
        );
        
        if (!$checklist) {
            throw new Exception("Test checklist not found for project {$this->testProjectId}");
        }
        
        $checklistData = json_decode($checklist['checklist_data'], true);
        if (empty($checklistData)) {
            throw new Exception("No checklist items found to test");
        }
        
        // Find the first item to edit
        $testItem = null;
        $testItemId = null;
        foreach ($checklistData as $category => $items) {
            if (!empty($items)) {
                $testItem = $items[0];
                $testItemId = $testItem['id'];
                break;
            }
        }
        
        if (!$testItemId) {
            throw new Exception("No test item found");
        }
        
        // Perform edit operation
        $originalTitle = $testItem['title'];
        $newTitle = $originalTitle . " (Test Edit " . date('H:i:s') . ")";
        
        $result = $this->clientProject->editChecklistItem(
            $this->testProjectId,
            $testItemId,
            [
                'title' => $newTitle,
                'description' => $testItem['description'] ?? '',
                'category' => $testItem['category'] ?? 'General',
                'completed' => $testItem['completed'] ?? false
            ]
        );
        
        if (!$result) {
            throw new Exception("Single edit operation failed");
        }
        
        echo "✅ Single edit operation successful\n\n";
    }
    
    /**
     * Test sequential edit operations
     */
    private function testSequentialEdits() {
        echo "🔍 Testing sequential edit operations...\n";
        
        // Get current checklist
        $checklist = $this->db->fetch(
            "SELECT * FROM project_checklists WHERE project_id = ?",
            [$this->testProjectId]
        );
        
        $checklistData = json_decode($checklist['checklist_data'], true);
        
        // Find multiple items to edit
        $testItems = [];
        foreach ($checklistData as $category => $items) {
            foreach ($items as $item) {
                if (count($testItems) < 3) {
                    $testItems[] = $item;
                }
            }
        }
        
        if (count($testItems) < 2) {
            throw new Exception("Need at least 2 items for sequential edit test");
        }
        
        // Perform sequential edits
        $timestamp = date('H:i:s');
        for ($i = 0; $i < count($testItems); $i++) {
            $item = $testItems[$i];
            $newTitle = $item['title'] . " (Sequential Edit $i at $timestamp)";
            
            $result = $this->clientProject->editChecklistItem(
                $this->testProjectId,
                $item['id'],
                [
                    'title' => $newTitle,
                    'description' => $item['description'] ?? '',
                    'category' => $item['category'] ?? 'General',
                    'completed' => $item['completed'] ?? false
                ]
            );
            
            if (!$result) {
                throw new Exception("Sequential edit operation $i failed");
            }
            
            echo "  ✓ Sequential edit $i completed\n";
            
            // Small delay between operations
            usleep(100000); // 100ms
        }
        
        echo "✅ Sequential edit operations successful\n\n";
    }
    
    /**
     * Test concurrent operations simulation
     */
    private function testConcurrentOperations() {
        echo "🔍 Testing concurrent operations simulation...\n";
        
        // This simulates rapid-fire operations that might cause conflicts
        $checklist = $this->db->fetch(
            "SELECT * FROM project_checklists WHERE project_id = ?",
            [$this->testProjectId]
        );
        
        $checklistData = json_decode($checklist['checklist_data'], true);
        $testItem = null;
        foreach ($checklistData as $items) {
            if (!empty($items)) {
                $testItem = $items[0];
                break;
            }
        }
        
        if (!$testItem) {
            throw new Exception("No test item found for concurrent operations test");
        }
        
        // Perform rapid operations
        $timestamp = date('H:i:s');
        for ($i = 0; $i < 5; $i++) {
            $newTitle = $testItem['title'] . " (Rapid Edit $i at $timestamp)";
            
            $result = $this->clientProject->editChecklistItem(
                $this->testProjectId,
                $testItem['id'],
                [
                    'title' => $newTitle,
                    'description' => $testItem['description'] ?? '',
                    'category' => $testItem['category'] ?? 'General',
                    'completed' => $testItem['completed'] ?? false
                ]
            );
            
            if (!$result) {
                throw new Exception("Rapid operation $i failed");
            }
            
            echo "  ✓ Rapid operation $i completed\n";
        }
        
        echo "✅ Concurrent operations simulation successful\n\n";
    }
    
    /**
     * Test retry logic by simulating connection issues
     */
    private function testRetryLogic() {
        echo "🔍 Testing retry logic...\n";
        
        // Test connection health check
        $isHealthy = $this->db->isConnectionHealthy();
        if (!$isHealthy) {
            echo "  ⚠️  Connection not healthy, testing reconnection...\n";
            $reconnected = $this->db->reconnect();
            if (!$reconnected) {
                throw new Exception("Failed to reconnect to database");
            }
            echo "  ✓ Reconnection successful\n";
        }
        
        // Test error detection methods
        $testException = new PDOException("Deadlock found when trying to get lock", '40001');
        $isDeadlock = $this->db->isDeadlockError($testException);
        if (!$isDeadlock) {
            throw new Exception("Deadlock detection failed");
        }
        echo "  ✓ Deadlock detection working\n";
        
        $testConnectionException = new PDOException("MySQL server has gone away", '2006');
        $isConnectionError = $this->db->isConnectionError($testConnectionException);
        if (!$isConnectionError) {
            throw new Exception("Connection error detection failed");
        }
        echo "  ✓ Connection error detection working\n";
        
        echo "✅ Retry logic components working correctly\n\n";
    }
}

// Run the tests
if (php_sapi_name() === 'cli') {
    $test = new ChecklistRobustnessTest();
    $test->runTests();
} else {
    echo "<pre>";
    $test = new ChecklistRobustnessTest();
    $test->runTests();
    echo "</pre>";
}
?>
