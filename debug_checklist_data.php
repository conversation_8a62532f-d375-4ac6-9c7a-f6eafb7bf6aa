<?php
require_once 'config/config.php';
require_once 'src/utils/Database.php';

$db = Database::getInstance();

echo "🔍 Debugging Checklist Data for Project 56\n";
echo "==========================================\n\n";

// Check if project exists
$project = $db->fetch('SELECT * FROM projects WHERE id = 56');
if (!$project) {
    echo "❌ Project 56 not found\n";
    exit;
}

echo "✅ Project found: " . $project['name'] . "\n\n";

// Check checklist data
$checklists = $db->fetchAll('SELECT * FROM project_checklists WHERE project_id = 56');
echo "📋 Checklist count: " . count($checklists) . "\n\n";

if (empty($checklists)) {
    echo "❌ No checklists found for project 56\n";
    echo "Let's check if there are any checklist templates available:\n\n";
    
    $templates = $db->fetchAll('SELECT * FROM project_checklist_templates WHERE is_default = 1 OR user_id = ?', [$project['user_id']]);
    echo "📝 Available templates: " . count($templates) . "\n";
    
    if (!empty($templates)) {
        foreach ($templates as $template) {
            echo "  - " . $template['template_name'] . " (ID: " . $template['id'] . ")\n";
        }
        echo "\nTo create a checklist, visit: http://localhost/momentum/client-projects/checklist/56\n";
        echo "And click 'Create Checklist' to use a template.\n";
    }
} else {
    foreach ($checklists as $index => $checklist) {
        echo "📋 Checklist " . ($index + 1) . ":\n";
        echo "  - ID: " . $checklist['id'] . "\n";
        echo "  - Name: " . $checklist['checklist_name'] . "\n";
        echo "  - Completion: " . $checklist['completion_percentage'] . "%\n";
        echo "  - Created: " . $checklist['created_at'] . "\n";
        echo "  - Updated: " . $checklist['updated_at'] . "\n";
        
        $checklistData = json_decode($checklist['checklist_data'], true);
        if ($checklistData) {
            echo "  - Categories: " . count($checklistData) . "\n";
            $totalItems = 0;
            $completedItems = 0;
            
            foreach ($checklistData as $categoryName => $items) {
                echo "    * " . $categoryName . ": " . count($items) . " items\n";
                foreach ($items as $item) {
                    $totalItems++;
                    if ($item['completed'] ?? false) {
                        $completedItems++;
                    }
                }
            }
            
            echo "  - Total items: $totalItems\n";
            echo "  - Completed items: $completedItems\n";
            echo "  - Calculated completion: " . ($totalItems > 0 ? round(($completedItems / $totalItems) * 100) : 0) . "%\n";
        } else {
            echo "  - ❌ Invalid JSON data\n";
            echo "  - Raw data: " . substr($checklist['checklist_data'], 0, 100) . "...\n";
        }
        echo "\n";
    }
}

// Test the model method
echo "🧪 Testing ClientProject model method:\n";
require_once 'src/models/ClientProject.php';
$clientProject = new ClientProject();
$modelResult = $clientProject->getProjectChecklist(56);
echo "Model result count: " . count($modelResult) . "\n";

if (!empty($modelResult)) {
    echo "First result structure:\n";
    print_r(array_keys($modelResult[0]));
}
?>
